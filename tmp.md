Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
\_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
\_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
🔥 Initializing Firebase... firebase-config.js:22:13
✅ Firebase App initialized successfully firebase-config.js:24:13
✅ Firebase Auth initialized successfully firebase-config.js:27:13
✅ Firestore initialized successfully firebase-config.js:30:13
✅ Firebase Analytics initialized successfully firebase-config.js:35:17
[2025-07-27T09:57:32.634Z] @firebase/firestore: Firestore (10.7.1): enableIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead. <anonymous code>:1:145535
🔥 Firebase Auth Manager initialized firebase-config.js:94:17
📱 Firebase App: INITIALIZED firebase-config.js:95:17
🗄️ Firestore DB: CONNECTED firebase-config.js:96:17
🎉 Firebase Auth Manager created and available globally firebase-config.js:643:13
🔍 Firebase Initialization Status:
Object { app: true, auth: true, db: true, analytics: true, manager: true }
firebase-config.js:647:13
🔍 Firebase ready, checking authentication... index.html:6339:25
🔍 Starting safe auth check... auth-fix.js:83:13
✅ Firebase is ready auth-fix.js:64:21
🔐 Auth status: false auth-fix.js:96:21
👤 User info:
Object { user: null, profile: null, isAuthenticated: false, isAdmin: null }
auth-fix.js:97:21
❌ User not authenticated, redirecting to login index.html:6359:33
🔄 Safe redirect to: login.html (attempt 1/3) auth-fix.js:45:13
✅ Main content layout fixed index.html:344:21
✅ Sidebar layout fixed index.html:354:21
🎉 Emergency layout fix applied successfully index.html:358:19
🔧 Forcing admin content to show... emergency-loading-fix.js:16:17
✅ Loading indicator hidden emergency-loading-fix.js:26:25
✅ Main content forced visible emergency-loading-fix.js:39:25
✅ Sidebar forced visible emergency-loading-fix.js:48:25
✅ 20 content sections forced visible emergency-loading-fix.js:60:25
🎉 Emergency loading fix completed successfully emergency-loading-fix.js:76:21
🚀 Initializing dashboard coordination system... dashboard-coordination.js:129:17
✅ Enhanced dashboard detected by coordination system dashboard-coordination.js:40:25
🛡️ Dashboard cleanup disabled dashboard-coordination.js:53:21
✅ Dashboard coordination system initialized dashboard-coordination.js:150:17
✅ API Fix Script initialized api-fix.js:283:13
⚡ Quick Fix - DOM Ready quick-fix.js:335:13
🔧 Navigation Fix - DOM Content Loaded navigation-fix.js:303:13
🚨 Emergency Navigation Fix - DOM Ready emergency-navigation-fix.js:337:13
🔧 Final Navigation Fix - DOM Ready final-navigation-fix.js:420:13
🚀 Initializing popup removal system... remove-diagnostic-popups.js:182:13
🧹 Removing all diagnostic popups... remove-diagnostic-popups.js:10:13
🧹 Removed 0 diagnostic popups/elements remove-diagnostic-popups.js:58:13
🛡️ Setting up popup prevention... remove-diagnostic-popups.js:65:13
✅ Disabled popup function: displaySpecificResults remove-diagnostic-popups.js:93:21
✅ Disabled popup function: displayFixResults remove-diagnostic-popups.js:93:21
✅ Disabled popup function: displayUltimateResults remove-diagnostic-popups.js:93:21
✅ Disabled popup function: createDiagnosticsReport remove-diagnostic-popups.js:93:21
⏰ Starting cleanup interval... remove-diagnostic-popups.js:170:13
✅ Popup removal system initialized remove-diagnostic-popups.js:193:13
✅ NotificationManager already exists, preserving it cleanup-duplicates.js:17:21
✅ Event listeners for DOMContentLoaded marked as cleaned cleanup-duplicates.js:164:25
✅ Event listeners for load marked as cleaned cleanup-duplicates.js:164:25
✅ Event listeners for resize marked as cleaned cleanup-duplicates.js:164:25
✅ All cleanup validations passed cleanup-duplicates.js:205:21
🎉 Cleanup completed successfully cleanup-duplicates.js:224:25
✅ Shared utilities initialized utils.js:509:13
🌐 API Call: ../api/get-ai-settings.php utils.js:14:17
🌐 API Call: ../api/get-ai-settings.php api-fix.js:14:17
🔧 Initialisation des corrections Firebase/Analytics... firebase-analytics-fix.js:148:13
🔧 Initialisation des corrections Firebase/Analytics... firebase-analytics-fix.js:8:13
🚫 Désactivation de Google Analytics en mode développement firebase-analytics-fix.js:89:17
✅ Corrections Firebase/Analytics appliquées firebase-analytics-fix.js:157:13
DOM Content Loaded - Starting admin initialization... admin.js:2898:13
🌐 API Call: ../php/admin.php?action=check api-fix.js:14:17
Initializing navigation... admin.js:634:13
Found navigation items: 19 admin.js:638:13
Adding click listener to item 0:

<li data-section="reportsContent" onclick="showAdminSection('reportsContent')">
admin.js:646:17
Adding click listener to item 1:
<li data-section="storesManagementContent" onclick="showAdminSection('storesManagementContent')">
admin.js:646:17
Adding click listener to item 2:
<li class="active" data-section="dashboard">
admin.js:646:17
Adding click listener to item 3:
<li data-section="books">
admin.js:646:17
Adding click listener to item 4:
<li data-section="orders">
admin.js:646:17
Adding click listener to item 5:
<li data-section="landingPages">
admin.js:646:17
Adding click listener to item 6:
<li data-section="reportsContent">
admin.js:646:17
Adding click listener to item 7:
<li class="admin-settings-menu expanded">
admin.js:646:17
Adding click listener to item 8:
<li data-section="generalSettings" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 9:
<li data-section="paymentSettings" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 10:
<li data-section="categories" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 11:
<li data-section="usersManagement" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 12:
<li data-section="rolesManagement" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 13:
<li data-section="showcaseDemo" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 14:
<li data-section="storeSettings" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 15:
<li data-section="storesManagementContent" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 16:
<li data-section="securitySettings" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 17:
<li data-section="subscriptionsManagement" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Adding click listener to item 18:
<li onclick="toggleDiagnosticsPanel()" style="display: flex; visibilit… 14px 18px; z-index: 2;">
admin.js:646:17
Navigation initialization complete admin.js:784:13
Initializing form handlers... admin.js:1399:13
Product form not found in DOM yet... admin.js:1404:17
Initializing settings handlers... admin.js:1736:13
Found setting items: 0 admin.js:1741:13
Starting TinyMCE initialization... admin.js:1303:13
No textareas found, waiting for DOM... <anonymous code>:1:145535
🛡️ loadDashboard() blocked by coordination system dashboard-coordination.js:69:25
Admin initialization complete admin.js:2938:13
💳 Payment settings DOM loaded payment-settings.js:183:13
🚀 Initializing critical fixes... critical-fixes.js:18:17
🔧 Fixing product management functions... critical-fixes.js:36:17
✅ Product management functions fixed critical-fixes.js:180:17
🔧 Fixing pagination functions... critical-fixes.js:184:17
✅ Pagination functions fixed critical-fixes.js:227:17
🔧 Fixing settings sections... critical-fixes.js:231:17
✅ Settings sections fixed critical-fixes.js:288:17
🔧 Fixing reports section... critical-fixes.js:292:17
✅ Reports section fixed critical-fixes.js:315:17
✅ Critical fixes initialized critical-fixes.js:32:17
🔒 تهيئة إعدادات الأمان... security-settings.js:303:13
🌐 API Call: ../api/security-settings.php api-fix.js:14:17
✅ Security settings initialized security-settings.js:534:13
🌐 API Call: php/api/security-settings.php?action=dashboard api-fix.js:14:17
🔄 API URL fixed: php/api/security-settings.php?action=dashboard → php/api/security-settings.php?action=dashboard api-fix.js:43:29
🌐 API Call: php/api/security-settings.php?action=password_policy api-fix.js:14:17
🔄 API URL fixed: php/api/security-settings.php?action=password_policy → php/api/security-settings.php?action=password_policy api-fix.js:43:29
🌐 API Call: php/api/security-settings.php?action=threat_detection api-fix.js:14:17
🔄 API URL fixed: php/api/security-settings.php?action=threat_detection → php/api/security-settings.php?action=threat_detection api-fix.js:43:29
📦 Products Loader Fix ready products-loader-fix.js:280:17
🚀 Initializing products pagination... products-pagination.js:21:17
📦 Loading products with pagination... products-pagination.js:36:21
🌐 API Call: ../php/api/products.php api-fix.js:14:17
✅ Multi-User Admin Interface ready multi-user-admin-interface.js:486:17
✅ Enhanced dashboard already present - skipping multi-user dashboard multi-user-admin-interface.js:495:25
✅ Reports and Statistics module ready reports-statistics.js:427:17
🚀 Initializing Enhanced Admin Settings Menu from external script... admin-settings-menu-enhanced.js:397:13
🚀 Initializing Enhanced Admin Settings Menu... admin-settings-menu-enhanced.js:21:17
✅ Enhanced Admin Settings Menu initialized successfully admin-settings-menu-enhanced.js:60:17
✅ Enhanced Admin Settings Menu available globally admin-settings-menu-enhanced.js:403:13
🌐 API Call: /api/get-ai-settings.php api-fix.js:14:17
🚨 Emergency submenu visibility fix activated index.html:5230:21
✅ Item 1 forced visible: الإعدادات العامة index.html:5266:29
✅ Item 2 forced visible: إعدادات الدفع index.html:5266:29
✅ Item 3 forced visible: إدارة الفئات index.html:5266:29
✅ Item 4 forced visible: إدارة المستخدمين index.html:5266:29
✅ Item 5 forced visible: إدارة الأدوار index.html:5266:29
✅ Item 6 forced visible: عرض النظام المحسن index.html:5266:29
✅ Item 7 forced visible: إعدادات المتجر index.html:5266:29
✅ Item 8 forced visible: إدارة المتاجر index.html:5266:29
✅ Item 9 forced visible: إعدادات الأمان index.html:5266:29
✅ Item 10 forced visible: إدارة الاشتراكات index.html:5266:29
✅ Item 11 forced visible: تشخيص النظام index.html:5266:29
✅ Submenu forced visible with 11 items index.html:5269:25
✅ Menu forced to expanded state index.html:5278:25
🛡️ Activating enhanced dashboard protection system index.html:6094:19
✅ Dashboard protection system activated index.html:6110:19
🔥 Firebase request failed: Firebase config blocked firebase-enhanced-fix.js:89:25
🔄 Retrying Firebase request in 2000ms (attempt 1/3) firebase-enhanced-fix.js:96:29
✅ Main content layout fixed index.html:344:21
✅ Sidebar layout fixed index.html:354:21
🎉 Emergency layout fix applied successfully index.html:358:19
🚨 Emergency submenu visibility fix activated index.html:5230:21
✅ Item 1 forced visible: الإعدادات العامة index.html:5266:29
✅ Item 2 forced visible: إعدادات الدفع index.html:5266:29
✅ Item 3 forced visible: إدارة الفئات index.html:5266:29
✅ Item 4 forced visible: إدارة المستخدمين index.html:5266:29
✅ Item 5 forced visible: إدارة الأدوار index.html:5266:29
✅ Item 6 forced visible: عرض النظام المحسن index.html:5266:29
✅ Item 7 forced visible: إعدادات المتجر index.html:5266:29
✅ Item 8 forced visible: إدارة المتاجر index.html:5266:29
✅ Item 9 forced visible: إعدادات الأمان index.html:5266:29
✅ Item 10 forced visible: إدارة الاشتراكات index.html:5266:29
✅ Item 11 forced visible: تشخيص النظام index.html:5266:29
✅ Submenu forced visible with 11 items index.html:5269:25
✅ Menu forced to expanded state index.html:5278:25
💳 Initializing Payment Settings... payment-settings.js:76:13
Loading: جاري تحميل إعدادات الدفع... 2 stores-management.js:487:13
🌐 API Call: ../php/api/payment-settings.php api-fix.js:14:17
🧹 Forcing clean navigation state... admin-sections-fix.js:273:17
✅ Clean state enforced with !important styles admin-sections-fix.js:302:17
🚀 Initializing navigation system... admin-sections-fix.js:22:17
📄 Showing section: dashboard admin-sections-fix.js:51:17
📦 Loading content for section: dashboard admin-sections-fix.js:153:17
✅ Enhanced dashboard detected - preserving existing content admin-sections-fix.js:164:29
✅ Section dashboard is now active admin-sections-fix.js:66:21
🔗 Setting up navigation listeners... admin-sections-fix.js:89:17
Found 17 navigation items admin-sections-fix.js:93:17
✅ Listener added to: reportsContent admin-sections-fix.js:112:21
✅ Listener added to: storesManagementContent admin-sections-fix.js:112:21
✅ Listener added to: dashboard admin-sections-fix.js:112:21
✅ Listener added to: books admin-sections-fix.js:112:21
✅ Listener added to: orders admin-sections-fix.js:112:21
✅ Listener added to: landingPages admin-sections-fix.js:112:21
✅ Listener added to: reportsContent admin-sections-fix.js:112:21
✅ Listener added to: generalSettings admin-sections-fix.js:112:21
✅ Listener added to: paymentSettings admin-sections-fix.js:112:21
✅ Listener added to: categories admin-sections-fix.js:112:21
✅ Listener added to: usersManagement admin-sections-fix.js:112:21
✅ Listener added to: rolesManagement admin-sections-fix.js:112:21
✅ Listener added to: showcaseDemo admin-sections-fix.js:112:21
✅ Listener added to: storeSettings admin-sections-fix.js:112:21
✅ Listener added to: storesManagementContent admin-sections-fix.js:112:21
✅ Listener added to: securitySettings admin-sections-fix.js:112:21
✅ Listener added to: subscriptionsManagement admin-sections-fix.js:112:21
✅ Navigation listeners setup complete admin-sections-fix.js:132:17
✅ Navigation system initialized admin-sections-fix.js:33:17
🔄 Auto-expanding admin settings menu... admin-settings-menu-enhanced.js:48:21
📈 Expanding admin settings menu... admin-settings-menu-enhanced.js:179:17
✅ Admin settings menu expanded admin-settings-menu-enhanced.js:198:17
🔧 Auth Fix Script loaded auth-fix.js:2:9
🔧 Auth Fix Script initialized auth-fix.js:316:9
📊 Redirect count: 1 auth-fix.js:317:9
🌐 Current URL: http://localhost:8000/admin/login.html auth-fix.js:318:9
📄 Page type: 255 auth-fix.js:319:9
🔥 Initializing Firebase... firebase-config.js:22:13
✅ Firebase App initialized successfully firebase-config.js:24:13
✅ Firebase Auth initialized successfully firebase-config.js:27:13
✅ Firestore initialized successfully firebase-config.js:30:13
✅ Firebase Analytics initialized successfully firebase-config.js:35:17
[2025-07-27T09:57:34.872Z]  @firebase/firestore: Firestore (10.7.1): enableIndexedDbPersistence() will be deprecated in the future, you can use `FirestoreSettings.cache` instead. <anonymous code>:1:145535
🔥 Firebase Auth Manager initialized firebase-config.js:94:17
📱 Firebase App: INITIALIZED firebase-config.js:95:17
🗄️ Firestore DB: CONNECTED firebase-config.js:96:17
🎉 Firebase Auth Manager created and available globally firebase-config.js:643:13
🔍 Firebase Initialization Status:
Object { app: true, auth: true, db: true, analytics: true, manager: true }
firebase-config.js:647:13
🔧 Login page loaded - auth-fix.js will handle redirections login.html:565:15
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
🔐 Firebase user state changed: <NAME_EMAIL> firebase-config.js:116:33
📱 Login page mode: using minimal profile firebase-config.js:135:37
🔍 About to call onUserSignedIn - isLoginPage: true loginFormSubmitted: undefined firebase-config.js:139:33
🔐 User signed in: <EMAIL> firebase-config.js:362:17
🔐 Firebase user signed in: <EMAIL> auth-fix.js:113:13
👤 User profile:
Object { uid: "ZwKPeWwHUdNphDcALc8tsteOu5Z2", email: "<EMAIL>", displayName: "admin", role: "user", isActive: true, loginPageMode: true }
auth-fix.js:114:13
🔍 Manual login check:
Object { windowLoginFormSubmitted: undefined, sessionManualLogin: false, timeSinceLogin: 1753610255599, isRecentLogin: false, isManualLogin: false, currentPath: "/admin/login.html" }
auth-fix.js:135:17
🚨 User was redirected from dashboard - NOT auto-redirecting to prevent loop auth-fix.js:149:25
💡 User needs to manually log in again auth-fix.js:150:25
La valeur de l’attribut « expires » pour le cookie « \_ga_NXQWCWG5YD » a été écrasée. login.html
✅ Firestore offline persistence enabled firebase-config.js:49:17
