<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Redirect Fix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.danger:hover {
            background: #da190b;
        }
        .status {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border-left: 4px solid #4CAF50;
        }
        .error {
            background: rgba(244, 67, 54, 0.2);
            border-left: 4px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Force Redirect Fix Tool</h1>
        <p>أداة لإصلاح مشكلة عدم التوجيه بعد تسجيل الدخول</p>
        
        <div class="status" id="status">
            جاري فحص الحالة الحالية...
        </div>
        
        <button class="btn" onclick="checkCurrentState()">🔍 فحص الحالة الحالية</button>
        <button class="btn" onclick="forceRedirect()">🚀 فرض التوجيه إلى Dashboard</button>
        <button class="btn" onclick="clearAllFlags()">🧹 مسح جميع الـ Flags</button>
        <button class="btn danger" onclick="resetAuth()">🔄 إعادة تعيين المصادقة</button>
        
        <hr style="margin: 30px 0; border: 1px solid rgba(255,255,255,0.3);">
        
        <h3>🔧 إصلاحات متقدمة</h3>
        <button class="btn" onclick="fixAuthManager()">🛠️ إصلاح Firebase Auth Manager</button>
        <button class="btn" onclick="simulateManualLogin()">👤 محاكاة تسجيل دخول يدوي</button>
        <button class="btn" onclick="bypassAllChecks()">⚡ تجاوز جميع الفحوصات</button>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        function checkCurrentState() {
            updateStatus('🔍 فحص الحالة الحالية...', 'info');
            
            const state = {
                currentUrl: window.location.href,
                firebaseAuth: !!window.firebaseAuth,
                firebaseAuthManager: !!window.firebaseAuthManager,
                currentUser: window.firebaseAuth?.currentUser?.email || 'غير متصل',
                loginFormSubmitted: window.loginFormSubmitted,
                sessionManualLogin: sessionStorage.getItem('manualLogin'),
                sessionManualLoginTime: sessionStorage.getItem('manualLoginTime'),
                redirectedFromDashboard: sessionStorage.getItem('redirectedFromDashboard'),
                safeRedirectFunction: typeof window.safeRedirect === 'function'
            };
            
            let statusText = '📊 الحالة الحالية:\n\n';
            for (const [key, value] of Object.entries(state)) {
                statusText += `${key}: ${value}\n`;
            }
            
            updateStatus(statusText, 'info');
        }

        function forceRedirect() {
            updateStatus('🚀 محاولة فرض التوجيه...', 'info');
            
            try {
                // Method 1: Use safeRedirect if available
                if (typeof window.safeRedirect === 'function') {
                    updateStatus('✅ استخدام safeRedirect...', 'success');
                    window.safeRedirect('../index.html');
                    return;
                }
                
                // Method 2: Direct redirect
                updateStatus('⚠️ safeRedirect غير متوفر، استخدام التوجيه المباشر...', 'info');
                window.location.href = '../index.html';
                
            } catch (error) {
                updateStatus('❌ فشل في التوجيه: ' + error.message, 'error');
            }
        }

        function clearAllFlags() {
            updateStatus('🧹 مسح جميع الـ Flags...', 'info');
            
            // Clear window flags
            window.loginFormSubmitted = false;
            window.redirectInProgress = false;
            
            // Clear sessionStorage
            sessionStorage.removeItem('manualLogin');
            sessionStorage.removeItem('manualLoginTime');
            sessionStorage.removeItem('redirectedFromDashboard');
            sessionStorage.removeItem('autoRedirectForLoggedInUser');
            
            updateStatus('✅ تم مسح جميع الـ Flags بنجاح', 'success');
        }

        function resetAuth() {
            updateStatus('🔄 إعادة تعيين المصادقة...', 'info');
            
            try {
                if (window.firebaseAuth && window.firebaseAuth.signOutUser) {
                    window.firebaseAuth.signOutUser();
                    updateStatus('✅ تم تسجيل الخروج بنجاح', 'success');
                } else {
                    updateStatus('⚠️ Firebase Auth غير متوفر', 'error');
                }
            } catch (error) {
                updateStatus('❌ فشل في تسجيل الخروج: ' + error.message, 'error');
            }
        }

        function fixAuthManager() {
            updateStatus('🛠️ محاولة إصلاح Firebase Auth Manager...', 'info');
            
            try {
                // Try to recreate auth manager if missing
                if (!window.firebaseAuthManager && window.firebaseAuth) {
                    updateStatus('🔧 إنشاء Auth Manager جديد...', 'info');
                    
                    // Simple auth manager recreation
                    window.firebaseAuthManager = {
                        getCurrentUser: () => window.firebaseAuth.currentUser,
                        getCurrentUserProfile: () => ({
                            uid: window.firebaseAuth.currentUser?.uid,
                            email: window.firebaseAuth.currentUser?.email,
                            role: 'admin',
                            isActive: true,
                            loginPageMode: true
                        })
                    };
                    
                    updateStatus('✅ تم إنشاء Auth Manager بنجاح', 'success');
                } else {
                    updateStatus('ℹ️ Auth Manager موجود بالفعل', 'info');
                }
            } catch (error) {
                updateStatus('❌ فشل في إصلاح Auth Manager: ' + error.message, 'error');
            }
        }

        function simulateManualLogin() {
            updateStatus('👤 محاكاة تسجيل دخول يدوي...', 'info');
            
            try {
                // Set all manual login flags
                window.loginFormSubmitted = true;
                sessionStorage.setItem('manualLogin', 'true');
                sessionStorage.setItem('manualLoginTime', Date.now().toString());
                
                // Clear any blocking flags
                sessionStorage.removeItem('redirectedFromDashboard');
                
                // Try to trigger the auth callback
                if (window.onFirebaseUserSignedIn && window.firebaseAuth?.currentUser) {
                    const user = window.firebaseAuth.currentUser;
                    const profile = {
                        uid: user.uid,
                        email: user.email,
                        role: 'admin',
                        isActive: true,
                        loginPageMode: true
                    };
                    
                    updateStatus('🔄 تشغيل callback المصادقة...', 'info');
                    window.onFirebaseUserSignedIn(user, profile);
                } else {
                    updateStatus('⚠️ لم يتم العثور على المستخدم أو callback', 'error');
                }
                
            } catch (error) {
                updateStatus('❌ فشل في محاكاة تسجيل الدخول: ' + error.message, 'error');
            }
        }

        function bypassAllChecks() {
            updateStatus('⚡ تجاوز جميع الفحوصات والتوجيه المباشر...', 'info');
            
            try {
                // Clear all blocking flags
                clearAllFlags();
                
                // Set manual login flags
                window.loginFormSubmitted = true;
                sessionStorage.setItem('manualLogin', 'true');
                sessionStorage.setItem('manualLoginTime', Date.now().toString());
                
                // Force redirect after short delay
                setTimeout(() => {
                    updateStatus('🚀 تنفيذ التوجيه...', 'success');
                    window.location.href = '../index.html';
                }, 1000);
                
            } catch (error) {
                updateStatus('❌ فشل في التجاوز: ' + error.message, 'error');
            }
        }

        // Auto-check state on load
        window.addEventListener('load', () => {
            setTimeout(checkCurrentState, 500);
        });
    </script>
</body>
</html>